SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: update_now_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_now_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
         BEGIN
          NEW.updated_at = now();
          RETURN NEW;
         END;
     $$;


--
-- Name: update_projected_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_projected_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
          BEGIN
            NEW.projected_at = now();

            RETURN NEW;
          END;
      $$;


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
          BEGIN
            IF NEW.updated_at is NULL THEN
              NEW.updated_at = now();
            END IF;

            RETURN NEW;
          END;
      $$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: answers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.answers (
    aggregate_id uuid NOT NULL,
    account_aggregate_id uuid NOT NULL,
    question_aggregate_id uuid NOT NULL,
    response_aggregate_id uuid NOT NULL,
    survey_to_question_aggregate_id uuid NOT NULL,
    select_option_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    additional_comment text,
    answerable_when_submitted boolean DEFAULT true,
    boolean_value boolean,
    mandatory boolean DEFAULT false,
    score integer DEFAULT '-1'::integer,
    select_option_data jsonb DEFAULT '{}'::jsonb NOT NULL,
    status text DEFAULT 'active'::text,
    text_value text,
    type text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    v integer DEFAULT 0,
    demographic_value_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    deleted_at timestamp with time zone,
    enps_11_score integer
);


--
-- Name: ar_internal_metadata; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ar_internal_metadata (
    key character varying NOT NULL,
    value character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: bulk_remove_grants_process; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.bulk_remove_grants_process (
    aggregate_id uuid NOT NULL,
    survey_id uuid NOT NULL,
    progress integer DEFAULT 0 NOT NULL,
    initial_grants_length integer DEFAULT 0 NOT NULL,
    executor_id uuid NOT NULL,
    succeded_at timestamp without time zone,
    failed_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL,
    updated_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);


--
-- Name: debezium_heartbeat; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.debezium_heartbeat (
    id integer NOT NULL,
    heartbeat_recorded timestamp without time zone NOT NULL
);


--
-- Name: ecst_feed_processing_lock; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ecst_feed_processing_lock (
    id uuid NOT NULL,
    account_id text NOT NULL,
    lock_key text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: factor_id_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.factor_id_mappings (
    id bigint NOT NULL,
    aggregate_id uuid NOT NULL,
    mongo_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: factor_id_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.factor_id_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: factor_id_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.factor_id_mappings_id_seq OWNED BY public.factor_id_mappings.id;


--
-- Name: factors; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.factors (
    aggregate_id uuid NOT NULL,
    account_aggregate_id uuid NOT NULL,
    survey_aggregate_id uuid NOT NULL,
    name jsonb DEFAULT '{}'::jsonb,
    cloned_from_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    "order" integer,
    v integer DEFAULT 0,
    code text,
    help text,
    long_desc text,
    short_desc text,
    status text DEFAULT 'active'::text,
    type text DEFAULT 'driver'::text,
    index_factor boolean DEFAULT false,
    custom_factor boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    projected_at timestamp with time zone
);


--
-- Name: kafka_outbox; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.kafka_outbox (
    id uuid NOT NULL,
    topic text NOT NULL,
    partition_key uuid NOT NULL,
    payload bytea,
    raw_payload jsonb
);


--
-- Name: question_id_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.question_id_mappings (
    id bigint NOT NULL,
    aggregate_id uuid NOT NULL,
    mongo_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: question_id_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.question_id_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: question_id_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.question_id_mappings_id_seq OWNED BY public.question_id_mappings.id;


--
-- Name: questions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questions (
    aggregate_id uuid NOT NULL,
    account_aggregate_id uuid NOT NULL,
    cloned_from_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    heads_of_hierarchy_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    code text,
    core_data_type text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    custom_other_option_label jsonb DEFAULT '{}'::jsonb NOT NULL,
    description jsonb DEFAULT '{}'::jsonb NOT NULL,
    event_sourced boolean DEFAULT false,
    help text,
    hierarchy_max_empty_fields integer DEFAULT 1,
    hierarchy_type text DEFAULT 'none'::text,
    last_known_commentable_state boolean DEFAULT false,
    name jsonb DEFAULT '{}'::jsonb NOT NULL,
    object_ref_type text,
    other_option boolean DEFAULT false,
    "order" integer DEFAULT 0,
    published boolean DEFAULT false,
    question_source_type text,
    scale text DEFAULT 'agreement'::text,
    selection_limit integer,
    self_review_description jsonb DEFAULT '{}'::jsonb NOT NULL,
    should_sort_naturally boolean DEFAULT false,
    status text DEFAULT 'active'::text,
    type text DEFAULT 'culture'::text,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    v integer,
    version integer DEFAULT 0,
    _type text,
    default_select_option_aggregate_id uuid,
    equivalent_to_aggregate_id uuid,
    factor_aggregate_id uuid,
    theme_aggregate_id uuid,
    deleted_at timestamp with time zone,
    projected_at timestamp with time zone,
    enps_11_scale boolean DEFAULT false
);


--
-- Name: raw_data_entity_bookmarks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.raw_data_entity_bookmarks (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    entity_type text,
    last_checked_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: raw_data_id_mapping_bookmarks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.raw_data_id_mapping_bookmarks (
    entity_type text NOT NULL,
    aggregate_id uuid NOT NULL,
    last_checked_at timestamp with time zone NOT NULL
);


--
-- Name: response_id_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.response_id_mappings (
    id bigint NOT NULL,
    aggregate_id uuid NOT NULL,
    mongo_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: response_id_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.response_id_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: response_id_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.response_id_mappings_id_seq OWNED BY public.response_id_mappings.id;


--
-- Name: response_short_codes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.response_short_codes (
    id bigint NOT NULL,
    short_code character varying NOT NULL,
    response_aggregate_id uuid NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    survey_aggregate_id uuid
);


--
-- Name: response_short_codes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.response_short_codes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: response_short_codes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.response_short_codes_id_seq OWNED BY public.response_short_codes.id;


--
-- Name: responses; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.responses (
    aggregate_id uuid NOT NULL,
    account_aggregate_id uuid,
    participant_aggregate_id uuid,
    redirect_aggregate_id uuid,
    reviewer_aggregate_id uuid,
    survey_aggregate_id uuid,
    survey_period_aggregate_id uuid,
    survey_submitted_by_aggregate_id uuid,
    survey_topic_aggregate_id uuid,
    user_aggregate_id uuid,
    demonstration boolean DEFAULT false,
    event_sourced boolean DEFAULT false,
    pause_notifications boolean DEFAULT false,
    survey_capture_via_kafka boolean DEFAULT false,
    configuration jsonb DEFAULT '{}'::jsonb,
    flags jsonb DEFAULT '{}'::jsonb,
    murmur_rating integer DEFAULT '-1'::integer,
    rating integer,
    v integer DEFAULT 0,
    version integer DEFAULT 0,
    capture_locale text,
    capture_method text,
    murmur_suggestion text,
    reminder_status text DEFAULT 'none'::text,
    secret text,
    splash_code text DEFAULT 'confirm'::text,
    status text DEFAULT 'active'::text,
    user_agent text,
    type text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    expired_at timestamp with time zone,
    started_at timestamp with time zone,
    submitted_at timestamp with time zone,
    deleted_at timestamp with time zone,
    projected_at timestamp with time zone
);


--
-- Name: role_assignments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.role_assignments (
    id uuid NOT NULL,
    role_key text NOT NULL,
    account_id text NOT NULL,
    user_aggregate_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    deleted_at timestamp with time zone
);


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schema_migrations (
    filename text,
    version text
);


--
-- Name: section_id_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.section_id_mappings (
    id bigint NOT NULL,
    aggregate_id uuid NOT NULL,
    mongo_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: section_id_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.section_id_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: section_id_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.section_id_mappings_id_seq OWNED BY public.section_id_mappings.id;


--
-- Name: sections; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sections (
    aggregate_id uuid NOT NULL,
    account_aggregate_id uuid NOT NULL,
    survey_aggregate_id uuid NOT NULL,
    name jsonb DEFAULT '{}'::jsonb,
    short_desc jsonb DEFAULT '{}'::jsonb,
    long_desc jsonb DEFAULT '{}'::jsonb,
    type text DEFAULT 'culture'::text,
    code text,
    "order" integer,
    v integer DEFAULT 0,
    status text DEFAULT 'active'::text,
    relationship_filters text[] DEFAULT '{}'::text[],
    cloned_from_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    event_sourced boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    projected_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- Name: select_option_id_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.select_option_id_mappings (
    id bigint NOT NULL,
    aggregate_id uuid NOT NULL,
    question_aggregate_id uuid NOT NULL,
    mongo_id text NOT NULL,
    question_mongo_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: select_option_id_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.select_option_id_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: select_option_id_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.select_option_id_mappings_id_seq OWNED BY public.select_option_id_mappings.id;


--
-- Name: select_options; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.select_options (
    aggregate_id uuid NOT NULL,
    account_aggregate_id uuid,
    question_aggregate_id uuid NOT NULL,
    employee_aggregate_id uuid,
    object_ref_aggregate_id uuid,
    demographic_value_aggregate_id uuid,
    value jsonb DEFAULT '{}'::jsonb,
    sort_term jsonb DEFAULT '{}'::jsonb,
    short_label jsonb DEFAULT '{}'::jsonb,
    cloned_from_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    "order" integer DEFAULT 0,
    status text DEFAULT 'active'::text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    range_from integer,
    range_to integer,
    deleted_at timestamp with time zone
);


--
-- Name: sms_service_account_integration; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sms_service_account_integration (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    account_aggregate_id uuid NOT NULL,
    twilio_account_sid text NOT NULL,
    twilio_auth_token_encrypted text NOT NULL,
    twilio_messaging_service_sid text
);


--
-- Name: sms_service_messages; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sms_service_messages (
    message_id uuid NOT NULL,
    user_aggregate_id uuid NOT NULL,
    account_aggregate_id uuid NOT NULL,
    message_content text NOT NULL,
    callback_url text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: sms_service_twilio_message_schedules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sms_service_twilio_message_schedules (
    schedule_id uuid NOT NULL,
    message_id uuid NOT NULL,
    twilio_message_id text,
    status text DEFAULT 'new'::text NOT NULL,
    send_at timestamp with time zone NOT NULL,
    sent_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: survey_id_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.survey_id_mappings (
    id bigint NOT NULL,
    aggregate_id uuid NOT NULL,
    mongo_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: survey_id_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.survey_id_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: survey_id_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.survey_id_mappings_id_seq OWNED BY public.survey_id_mappings.id;


--
-- Name: survey_to_question_id_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.survey_to_question_id_mappings (
    id bigint NOT NULL,
    aggregate_id uuid NOT NULL,
    mongo_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: survey_to_question_id_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.survey_to_question_id_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: survey_to_question_id_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.survey_to_question_id_mappings_id_seq OWNED BY public.survey_to_question_id_mappings.id;


--
-- Name: survey_to_questions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.survey_to_questions (
    aggregate_id uuid NOT NULL,
    account_aggregate_id uuid,
    question_aggregate_id uuid,
    survey_aggregate_id uuid,
    theme_aggregate_id uuid,
    segment_hierarchy_aggregate_id uuid,
    employee_hierarchy_aggregate_id uuid,
    capture boolean DEFAULT true,
    is_restricted boolean DEFAULT false,
    export_with_kiosk_code boolean DEFAULT false,
    mandatory boolean DEFAULT false,
    dependent_questions jsonb DEFAULT '{}'::jsonb,
    demographic_value_references jsonb DEFAULT '{}'::jsonb,
    condition_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    factor_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    user_type_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    reference_code text,
    status text DEFAULT 'active'::text,
    type text,
    "order" integer,
    v integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    projected_at timestamp with time zone,
    deleted_at timestamp with time zone
);


--
-- Name: surveys; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.surveys (
    aggregate_id uuid NOT NULL,
    account_aggregate_id uuid,
    owner_aggregate_id uuid,
    survey_capture_layout_aggregate_id uuid,
    survey_template_aggregate_id uuid,
    amnesty_schedule_aggregate_id uuid,
    continuous_schedule_aggregate_id uuid,
    participant_filter_aggregate_id uuid,
    snapshot_schedule_aggregate_id uuid,
    archived boolean DEFAULT false,
    benchmarkable boolean DEFAULT false,
    event_sourced boolean DEFAULT false,
    event_sourced_survey_demographics boolean DEFAULT false,
    event_sourced_survey_launch boolean DEFAULT false,
    attendee_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    custom_term_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    cloned_from_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    survey_participant_aggregate_ids uuid[] DEFAULT '{}'::uuid[],
    configuration jsonb DEFAULT '{}'::jsonb NOT NULL,
    description jsonb DEFAULT '{}'::jsonb NOT NULL,
    flags jsonb DEFAULT '{}'::jsonb NOT NULL,
    name jsonb DEFAULT '{}'::jsonb NOT NULL,
    options jsonb DEFAULT '{}'::jsonb NOT NULL,
    demographic_section_group_location text DEFAULT 'bottom'::text,
    demonstration_secret text,
    focus_algorithm text DEFAULT 'v1'::text,
    kiosk text DEFAULT 'inactive'::text,
    kiosk_key text,
    powerpoint_template text DEFAULT 'none'::text,
    response_welcome_message text,
    response_welcome_title text,
    status text DEFAULT 'design'::text,
    survey_period_type text DEFAULT 'snapshot'::text,
    template_type text,
    type text DEFAULT 'engagement'::text,
    significant_correlation_population_threshold double precision DEFAULT 0.5,
    survey_due_days integer DEFAULT 14,
    v integer DEFAULT 2,
    interview_due_days bigint DEFAULT 7,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    cached_at timestamp with time zone,
    closed_at timestamp with time zone,
    launched_at timestamp with time zone,
    communicated_close_at timestamp with time zone,
    event_source_consistency_checked_at timestamp with time zone,
    exclude_participants_after timestamp with time zone,
    comment_filter_stq_uuid uuid,
    projected_at timestamp with time zone
);


--
-- Name: temp_coach_engage_prototype_users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.temp_coach_engage_prototype_users (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    account_id uuid NOT NULL,
    user_context jsonb DEFAULT '{}'::jsonb NOT NULL
);


--
-- Name: unprocessable_demographics; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.unprocessable_demographics (
    id uuid NOT NULL,
    account_id text NOT NULL,
    demographic_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: user_session_blacklist; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_session_blacklist (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    created_by uuid NOT NULL,
    session_blocked_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- Name: user_survey_preferences; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_survey_preferences (
    aggregate_id uuid NOT NULL,
    user_id uuid NOT NULL,
    survey_id uuid NOT NULL,
    report_type text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    mongo_id text,
    mongo_user_id text,
    mongo_survey_id text,
    account_aggregate_id uuid
);


--
-- Name: verification_job_state; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.verification_job_state (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    entity_type text,
    verification_state jsonb DEFAULT '{}'::jsonb
);


--
-- Name: factor_id_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.factor_id_mappings ALTER COLUMN id SET DEFAULT nextval('public.factor_id_mappings_id_seq'::regclass);


--
-- Name: question_id_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.question_id_mappings ALTER COLUMN id SET DEFAULT nextval('public.question_id_mappings_id_seq'::regclass);


--
-- Name: response_id_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.response_id_mappings ALTER COLUMN id SET DEFAULT nextval('public.response_id_mappings_id_seq'::regclass);


--
-- Name: response_short_codes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.response_short_codes ALTER COLUMN id SET DEFAULT nextval('public.response_short_codes_id_seq'::regclass);


--
-- Name: section_id_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.section_id_mappings ALTER COLUMN id SET DEFAULT nextval('public.section_id_mappings_id_seq'::regclass);


--
-- Name: select_option_id_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.select_option_id_mappings ALTER COLUMN id SET DEFAULT nextval('public.select_option_id_mappings_id_seq'::regclass);


--
-- Name: survey_id_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.survey_id_mappings ALTER COLUMN id SET DEFAULT nextval('public.survey_id_mappings_id_seq'::regclass);


--
-- Name: survey_to_question_id_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.survey_to_question_id_mappings ALTER COLUMN id SET DEFAULT nextval('public.survey_to_question_id_mappings_id_seq'::regclass);


--
-- Name: answers answers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.answers
    ADD CONSTRAINT answers_pkey PRIMARY KEY (aggregate_id);


--
-- Name: ar_internal_metadata ar_internal_metadata_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ar_internal_metadata
    ADD CONSTRAINT ar_internal_metadata_pkey PRIMARY KEY (key);


--
-- Name: bulk_remove_grants_process bulk_remove_grants_process_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bulk_remove_grants_process
    ADD CONSTRAINT bulk_remove_grants_process_pkey PRIMARY KEY (aggregate_id);


--
-- Name: debezium_heartbeat debezium_heartbeat_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.debezium_heartbeat
    ADD CONSTRAINT debezium_heartbeat_pkey PRIMARY KEY (id);


--
-- Name: ecst_feed_processing_lock ecst_feed_processing_lock_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ecst_feed_processing_lock
    ADD CONSTRAINT ecst_feed_processing_lock_pkey PRIMARY KEY (id);


--
-- Name: factor_id_mappings factor_id_mappings_aggregate_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.factor_id_mappings
    ADD CONSTRAINT factor_id_mappings_aggregate_id_key UNIQUE (aggregate_id);


--
-- Name: factor_id_mappings factor_id_mappings_mongo_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.factor_id_mappings
    ADD CONSTRAINT factor_id_mappings_mongo_id_key UNIQUE (mongo_id);


--
-- Name: factor_id_mappings factor_id_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.factor_id_mappings
    ADD CONSTRAINT factor_id_mappings_pkey PRIMARY KEY (id);


--
-- Name: factors factors_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.factors
    ADD CONSTRAINT factors_pkey PRIMARY KEY (aggregate_id);


--
-- Name: kafka_outbox kafka_outbox_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.kafka_outbox
    ADD CONSTRAINT kafka_outbox_pkey PRIMARY KEY (id);


--
-- Name: question_id_mappings question_id_mappings_aggregate_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.question_id_mappings
    ADD CONSTRAINT question_id_mappings_aggregate_id_key UNIQUE (aggregate_id);


--
-- Name: question_id_mappings question_id_mappings_mongo_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.question_id_mappings
    ADD CONSTRAINT question_id_mappings_mongo_id_key UNIQUE (mongo_id);


--
-- Name: question_id_mappings question_id_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.question_id_mappings
    ADD CONSTRAINT question_id_mappings_pkey PRIMARY KEY (id);


--
-- Name: questions questions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questions
    ADD CONSTRAINT questions_pkey PRIMARY KEY (aggregate_id);


--
-- Name: raw_data_entity_bookmarks raw_data_entity_bookmarks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.raw_data_entity_bookmarks
    ADD CONSTRAINT raw_data_entity_bookmarks_pkey PRIMARY KEY (id);


--
-- Name: response_id_mappings response_id_mappings_aggregate_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.response_id_mappings
    ADD CONSTRAINT response_id_mappings_aggregate_id_key UNIQUE (aggregate_id);


--
-- Name: response_id_mappings response_id_mappings_mongo_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.response_id_mappings
    ADD CONSTRAINT response_id_mappings_mongo_id_key UNIQUE (mongo_id);


--
-- Name: response_id_mappings response_id_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.response_id_mappings
    ADD CONSTRAINT response_id_mappings_pkey PRIMARY KEY (id);


--
-- Name: response_short_codes response_short_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.response_short_codes
    ADD CONSTRAINT response_short_codes_pkey PRIMARY KEY (id);


--
-- Name: responses responses_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.responses
    ADD CONSTRAINT responses_pkey PRIMARY KEY (aggregate_id);


--
-- Name: role_assignments role_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_assignments
    ADD CONSTRAINT role_assignments_pkey PRIMARY KEY (id);


--
-- Name: section_id_mappings section_id_mappings_aggregate_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.section_id_mappings
    ADD CONSTRAINT section_id_mappings_aggregate_id_key UNIQUE (aggregate_id);


--
-- Name: section_id_mappings section_id_mappings_mongo_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.section_id_mappings
    ADD CONSTRAINT section_id_mappings_mongo_id_key UNIQUE (mongo_id);


--
-- Name: section_id_mappings section_id_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.section_id_mappings
    ADD CONSTRAINT section_id_mappings_pkey PRIMARY KEY (id);


--
-- Name: sections sections_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sections
    ADD CONSTRAINT sections_pkey PRIMARY KEY (aggregate_id);


--
-- Name: select_option_id_mappings select_option_id_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.select_option_id_mappings
    ADD CONSTRAINT select_option_id_mappings_pkey PRIMARY KEY (id);


--
-- Name: select_options select_options_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.select_options
    ADD CONSTRAINT select_options_pkey PRIMARY KEY (aggregate_id, question_aggregate_id);


--
-- Name: sms_service_account_integration sms_service_account_integration_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sms_service_account_integration
    ADD CONSTRAINT sms_service_account_integration_pkey PRIMARY KEY (id);


--
-- Name: sms_service_messages sms_service_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sms_service_messages
    ADD CONSTRAINT sms_service_messages_pkey PRIMARY KEY (message_id);


--
-- Name: sms_service_twilio_message_schedules sms_service_twilio_message_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sms_service_twilio_message_schedules
    ADD CONSTRAINT sms_service_twilio_message_schedules_pkey PRIMARY KEY (schedule_id);


--
-- Name: survey_id_mappings survey_id_mappings_aggregate_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.survey_id_mappings
    ADD CONSTRAINT survey_id_mappings_aggregate_id_key UNIQUE (aggregate_id);


--
-- Name: survey_id_mappings survey_id_mappings_mongo_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.survey_id_mappings
    ADD CONSTRAINT survey_id_mappings_mongo_id_key UNIQUE (mongo_id);


--
-- Name: survey_id_mappings survey_id_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.survey_id_mappings
    ADD CONSTRAINT survey_id_mappings_pkey PRIMARY KEY (id);


--
-- Name: survey_to_question_id_mappings survey_to_question_id_mappings_aggregate_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.survey_to_question_id_mappings
    ADD CONSTRAINT survey_to_question_id_mappings_aggregate_id_key UNIQUE (aggregate_id);


--
-- Name: survey_to_question_id_mappings survey_to_question_id_mappings_mongo_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.survey_to_question_id_mappings
    ADD CONSTRAINT survey_to_question_id_mappings_mongo_id_key UNIQUE (mongo_id);


--
-- Name: survey_to_question_id_mappings survey_to_question_id_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.survey_to_question_id_mappings
    ADD CONSTRAINT survey_to_question_id_mappings_pkey PRIMARY KEY (id);


--
-- Name: survey_to_questions survey_to_questions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.survey_to_questions
    ADD CONSTRAINT survey_to_questions_pkey PRIMARY KEY (aggregate_id);


--
-- Name: surveys surveys_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.surveys
    ADD CONSTRAINT surveys_pkey PRIMARY KEY (aggregate_id);


--
-- Name: temp_coach_engage_prototype_users temp_coach_engage_prototype_users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.temp_coach_engage_prototype_users
    ADD CONSTRAINT temp_coach_engage_prototype_users_pkey PRIMARY KEY (id);


--
-- Name: unprocessable_demographics unprocessable_demographics_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.unprocessable_demographics
    ADD CONSTRAINT unprocessable_demographics_pkey PRIMARY KEY (id);


--
-- Name: user_session_blacklist user_session_blacklist_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_session_blacklist
    ADD CONSTRAINT user_session_blacklist_pkey PRIMARY KEY (id);


--
-- Name: user_survey_preferences user_survey_preferences_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_survey_preferences
    ADD CONSTRAINT user_survey_preferences_pkey PRIMARY KEY (aggregate_id);


--
-- Name: verification_job_state verification_job_state_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.verification_job_state
    ADD CONSTRAINT verification_job_state_pkey PRIMARY KEY (id);


--
-- Name: answers_account_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX answers_account_aggregate_id_index ON public.answers USING btree (account_aggregate_id);


--
-- Name: answers_demographic_value_aggregate_ids_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX answers_demographic_value_aggregate_ids_index ON public.answers USING btree (demographic_value_aggregate_ids);


--
-- Name: answers_question_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX answers_question_aggregate_id_index ON public.answers USING btree (question_aggregate_id);


--
-- Name: answers_response_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX answers_response_aggregate_id_index ON public.answers USING btree (response_aggregate_id);


--
-- Name: answers_survey_to_question_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX answers_survey_to_question_aggregate_id_index ON public.answers USING btree (survey_to_question_aggregate_id);


--
-- Name: answers_updated_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX answers_updated_at_index ON public.answers USING btree (updated_at);


--
-- Name: bulk_remove_grants_process_survey_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX bulk_remove_grants_process_survey_id_index ON public.bulk_remove_grants_process USING btree (survey_id);


--
-- Name: ecst_feed_processing_lock_account_id_lock_key_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX ecst_feed_processing_lock_account_id_lock_key_index ON public.ecst_feed_processing_lock USING btree (account_id, lock_key);


--
-- Name: factor_id_mappings_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX factor_id_mappings_aggregate_id_index ON public.factor_id_mappings USING btree (aggregate_id);


--
-- Name: factor_id_mappings_mongo_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX factor_id_mappings_mongo_id_index ON public.factor_id_mappings USING btree (mongo_id);


--
-- Name: factors_account_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX factors_account_aggregate_id_index ON public.factors USING btree (account_aggregate_id);


--
-- Name: factors_code_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX factors_code_index ON public.factors USING btree (code);


--
-- Name: factors_projected_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX factors_projected_at_index ON public.factors USING btree (projected_at);


--
-- Name: factors_survey_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX factors_survey_aggregate_id_index ON public.factors USING btree (survey_aggregate_id);


--
-- Name: factors_updated_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX factors_updated_at_index ON public.factors USING btree (updated_at);


--
-- Name: index_response_short_codes_on_response_aggregate_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_response_short_codes_on_response_aggregate_id ON public.response_short_codes USING btree (response_aggregate_id);


--
-- Name: index_response_short_codes_on_short_code; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_response_short_codes_on_short_code ON public.response_short_codes USING btree (short_code);

--
-- Name: index_sms_service_account_integration_on_twilio_account_sid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_sms_service_account_integration_on_twilio_account_sid ON public.sms_service_account_integration USING btree (twilio_account_sid);

--
-- Name: index_sms_service_twilio_message_schedules_on_message_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sms_service_twilio_message_schedules_on_message_id ON public.sms_service_twilio_message_schedules USING btree (message_id);


--
-- Name: index_user_survey_preferences_user_id_survey_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_user_survey_preferences_user_id_survey_id ON public.user_survey_preferences USING btree (user_id, survey_id);


--
-- Name: question_id_mappings_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX question_id_mappings_aggregate_id_index ON public.question_id_mappings USING btree (aggregate_id);


--
-- Name: question_id_mappings_mongo_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX question_id_mappings_mongo_id_index ON public.question_id_mappings USING btree (mongo_id);


--
-- Name: questions__type_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX questions__type_index ON public.questions USING btree (_type);


--
-- Name: questions_account_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX questions_account_aggregate_id_index ON public.questions USING btree (account_aggregate_id);


--
-- Name: questions_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX questions_aggregate_id_index ON public.questions USING btree (aggregate_id);


--
-- Name: questions_code_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX questions_code_index ON public.questions USING btree (code);


--
-- Name: questions_projected_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX questions_projected_at_index ON public.questions USING btree (projected_at);


--
-- Name: questions_question_source_type_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX questions_question_source_type_index ON public.questions USING btree (question_source_type);


--
-- Name: raw_data_entity_bookmarks_entity_type_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX raw_data_entity_bookmarks_entity_type_index ON public.raw_data_entity_bookmarks USING btree (entity_type);


--
-- Name: raw_data_entity_bookmarks_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX raw_data_entity_bookmarks_id_index ON public.raw_data_entity_bookmarks USING btree (id);


--
-- Name: raw_data_id_mapping_bookmarks_entity_type_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX raw_data_id_mapping_bookmarks_entity_type_index ON public.raw_data_id_mapping_bookmarks USING btree (entity_type);


--
-- Name: response_id_mappings_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX response_id_mappings_aggregate_id_index ON public.response_id_mappings USING btree (aggregate_id);


--
-- Name: response_id_mappings_mongo_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX response_id_mappings_mongo_id_index ON public.response_id_mappings USING btree (mongo_id);


--
-- Name: responses_account_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX responses_account_aggregate_id_index ON public.responses USING btree (account_aggregate_id);


--
-- Name: responses_participant_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX responses_participant_aggregate_id_index ON public.responses USING btree (participant_aggregate_id);


--
-- Name: responses_projected_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX responses_projected_at_index ON public.responses USING btree (projected_at);


--
-- Name: responses_survey_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX responses_survey_aggregate_id_index ON public.responses USING btree (survey_aggregate_id);


--
-- Name: responses_updated_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX responses_updated_at_index ON public.responses USING btree (updated_at);


--
-- Name: responses_user_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX responses_user_aggregate_id_index ON public.responses USING btree (user_aggregate_id);


--
-- Name: role_assignments_account_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX role_assignments_account_id_index ON public.role_assignments USING btree (account_id);


--
-- Name: role_assignments_employee_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX role_assignments_employee_id_index ON public.role_assignments USING btree (user_aggregate_id);


--
-- Name: section_id_mappings_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX section_id_mappings_aggregate_id_index ON public.section_id_mappings USING btree (aggregate_id);


--
-- Name: section_id_mappings_mongo_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX section_id_mappings_mongo_id_index ON public.section_id_mappings USING btree (mongo_id);


--
-- Name: sections_code_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX sections_code_index ON public.sections USING btree (code);


--
-- Name: sections_projected_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX sections_projected_at_index ON public.sections USING btree (projected_at);


--
-- Name: sections_survey_aggregate_id_order_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX sections_survey_aggregate_id_order_index ON public.sections USING btree (survey_aggregate_id, "order");


--
-- Name: sections_updated_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX sections_updated_at_index ON public.sections USING btree (updated_at);


--
-- Name: select_option_id_mappings_question_aggregate_id_aggregate_id_in; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX select_option_id_mappings_question_aggregate_id_aggregate_id_in ON public.select_option_id_mappings USING btree (question_aggregate_id, aggregate_id);


--
-- Name: select_option_id_mappings_question_mongo_id_mongo_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX select_option_id_mappings_question_mongo_id_mongo_id_index ON public.select_option_id_mappings USING btree (question_mongo_id, mongo_id);


--
-- Name: select_options_account_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX select_options_account_aggregate_id_index ON public.select_options USING btree (account_aggregate_id);


--
-- Name: select_options_employee_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX select_options_employee_aggregate_id_index ON public.select_options USING btree (employee_aggregate_id);


--
-- Name: select_options_question_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX select_options_question_aggregate_id_index ON public.select_options USING btree (question_aggregate_id);


--
-- Name: sms_service_account_integration_account_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX sms_service_account_integration_account_aggregate_id_index ON public.sms_service_account_integration USING btree (account_aggregate_id);


--
-- Name: sms_service_messages_account_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX sms_service_messages_account_aggregate_id_index ON public.sms_service_messages USING btree (account_aggregate_id);


--
-- Name: sms_service_messages_user_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX sms_service_messages_user_aggregate_id_index ON public.sms_service_messages USING btree (user_aggregate_id);


--
-- Name: survey_id_mappings_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_id_mappings_aggregate_id_index ON public.survey_id_mappings USING btree (aggregate_id);


--
-- Name: survey_id_mappings_mongo_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_id_mappings_mongo_id_index ON public.survey_id_mappings USING btree (mongo_id);


--
-- Name: survey_to_question_id_mappings_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_to_question_id_mappings_aggregate_id_index ON public.survey_to_question_id_mappings USING btree (aggregate_id);


--
-- Name: survey_to_question_id_mappings_mongo_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_to_question_id_mappings_mongo_id_index ON public.survey_to_question_id_mappings USING btree (mongo_id);


--
-- Name: survey_to_questions_account_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_to_questions_account_aggregate_id_index ON public.survey_to_questions USING btree (account_aggregate_id);


--
-- Name: survey_to_questions_projected_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_to_questions_projected_at_index ON public.survey_to_questions USING btree (projected_at);


--
-- Name: survey_to_questions_question_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_to_questions_question_aggregate_id_index ON public.survey_to_questions USING btree (question_aggregate_id);


--
-- Name: survey_to_questions_survey_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_to_questions_survey_aggregate_id_index ON public.survey_to_questions USING btree (survey_aggregate_id);


--
-- Name: survey_to_questions_theme_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_to_questions_theme_aggregate_id_index ON public.survey_to_questions USING btree (theme_aggregate_id);


--
-- Name: survey_to_questions_updated_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX survey_to_questions_updated_at_index ON public.survey_to_questions USING btree (updated_at);


--
-- Name: surveys_account_aggregate_id_status_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX surveys_account_aggregate_id_status_index ON public.surveys USING btree (account_aggregate_id, status);


--
-- Name: surveys_closed_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX surveys_closed_at_index ON public.surveys USING btree (closed_at);


--
-- Name: surveys_projected_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX surveys_projected_at_index ON public.surveys USING btree (projected_at);


--
-- Name: surveys_updated_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX surveys_updated_at_index ON public.surveys USING btree (updated_at);


--
-- Name: unprocessable_demographics_account_id_demographic_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX unprocessable_demographics_account_id_demographic_id_index ON public.unprocessable_demographics USING btree (account_id, demographic_id);


--
-- Name: user_session_blacklist_user_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX user_session_blacklist_user_id_index ON public.user_session_blacklist USING btree (user_id);


--
-- Name: user_survey_preferences_account_aggregate_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX user_survey_preferences_account_aggregate_id_index ON public.user_survey_preferences USING btree (account_aggregate_id);


--
-- Name: verification_job_state_entity_type_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX verification_job_state_entity_type_index ON public.verification_job_state USING btree (entity_type);


--
-- Name: verification_job_state_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX verification_job_state_id_index ON public.verification_job_state USING btree (id);


--
-- Name: factor_id_mappings update_factor_id_mappings_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_factor_id_mappings_updated_at BEFORE INSERT OR UPDATE ON public.factor_id_mappings FOR EACH ROW EXECUTE FUNCTION public.update_now_updated_at_column();


--
-- Name: factors update_factors_projected_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_factors_projected_at BEFORE INSERT OR UPDATE ON public.factors FOR EACH ROW EXECUTE FUNCTION public.update_projected_at_column();


--
-- Name: question_id_mappings update_question_id_mappings_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_question_id_mappings_updated_at BEFORE INSERT OR UPDATE ON public.question_id_mappings FOR EACH ROW EXECUTE FUNCTION public.update_now_updated_at_column();


--
-- Name: questions update_questions_projected_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_questions_projected_at BEFORE INSERT OR UPDATE ON public.questions FOR EACH ROW EXECUTE FUNCTION public.update_projected_at_column();


--
-- Name: response_id_mappings update_response_id_mappings_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_response_id_mappings_updated_at BEFORE INSERT OR UPDATE ON public.response_id_mappings FOR EACH ROW EXECUTE FUNCTION public.update_now_updated_at_column();


--
-- Name: responses update_responses_projected_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_responses_projected_at BEFORE INSERT OR UPDATE ON public.responses FOR EACH ROW EXECUTE FUNCTION public.update_projected_at_column();


--
-- Name: section_id_mappings update_section_id_mappings_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_section_id_mappings_updated_at BEFORE INSERT OR UPDATE ON public.section_id_mappings FOR EACH ROW EXECUTE FUNCTION public.update_now_updated_at_column();


--
-- Name: sections update_sections_projected_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_sections_projected_at BEFORE UPDATE ON public.sections FOR EACH ROW EXECUTE FUNCTION public.update_projected_at_column();


--
-- Name: select_option_id_mappings update_select_option_id_mappings_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_select_option_id_mappings_updated_at BEFORE INSERT OR UPDATE ON public.select_option_id_mappings FOR EACH ROW EXECUTE FUNCTION public.update_now_updated_at_column();


--
-- Name: survey_id_mappings update_survey_id_mappings_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_survey_id_mappings_updated_at BEFORE INSERT OR UPDATE ON public.survey_id_mappings FOR EACH ROW EXECUTE FUNCTION public.update_now_updated_at_column();


--
-- Name: survey_to_question_id_mappings update_survey_to_question_id_mappings_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_survey_to_question_id_mappings_updated_at BEFORE INSERT OR UPDATE ON public.survey_to_question_id_mappings FOR EACH ROW EXECUTE FUNCTION public.update_now_updated_at_column();


--
-- Name: survey_to_questions update_survey_to_questions_projected_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_survey_to_questions_projected_at BEFORE INSERT OR UPDATE ON public.survey_to_questions FOR EACH ROW EXECUTE FUNCTION public.update_projected_at_column();


--
-- Name: surveys update_surveys_projected_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_surveys_projected_at BEFORE INSERT OR UPDATE ON public.surveys FOR EACH ROW EXECUTE FUNCTION public.update_projected_at_column();


--
-- Name: sms_service_twilio_message_schedules sms_service_twilio_message_schedules_message_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sms_service_twilio_message_schedules
    ADD CONSTRAINT sms_service_twilio_message_schedules_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.sms_service_messages(message_id);


--
-- PostgreSQL database dump complete
--

SET search_path TO "$user", public;

INSERT INTO "schema_migrations" (version) VALUES
('20250402013152'),
('20250402013529'),
('20250404063352'),
('20250411035523'),
('20250416004426'),
('20250427085801'),
('20250424042552'),
('20250424054018'),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL),
(NULL);
