require "rails_helper"

RSpec.describe SessionsController do
  let(:account) do
    instance_double(
      Account,
      id: "ACCOUNT_ID",
      subdomain: "nosaml",
      active_saml?: active_saml,
      status: :active,
      aggregate_id: SecureRandom.uuid
    )
  end
  let(:request_subdomain) { account.subdomain }
  let(:active_saml) { false }

  before do
    stub_const "Murmur::CULTURE_AMP_DOMAIN", "culture-amp.com"
    stub_const "Murmur::IDENTITY_ROOT", "identity.culture-amp.com"

    # for devise testing
    @request.env["devise.mapping"] = Devise.mappings[:user]
    @request.host = "#{request_subdomain}.#{Murmur::CULTURE_AMP_DOMAIN}"

    allow(Jobs::AnalyticsJob).to receive(:enqueue)
  end

  include_examples "a trackable controller"

  describe "#new" do
    let(:user) do
      instance_double(
        Person,
        account: account,
        email: "<EMAIL>",
        password: "IAMBATMAN",
        superuser?: false
      )
    end
    subject { get :new }

    it_behaves_like "a trackable action", feature: :authentication do
      let(:action) { subject }
    end

    context "when the subdomain does not belong to any account" do
      before do
        allow(Account).to receive(:subdomain).with(request_subdomain).and_return(nil)
        allow(Account).to receive(:subdomain).with(nil).and_return(nil)
      end

      it "renders the login page" do
        is_expected.to render_template("new", layout: "plain")
      end
    end

    context "when the subdomain belongs to an existing account" do
      before { allow(Account).to receive(:subdomain).with(request_subdomain).and_return(account) }

      context "when the account has no active saml integration" do
        let(:active_saml) { false }

        it { is_expected.to render_template("new", layout: "plain") }
      end

      context "when the account has active saml integration" do
        let(:active_saml) { true }
        let(:authenticator) do
          instance_double(
            AccountProperties::Authenticator,
            email_allowed?: email_allowed
          )
        end
        before do
          allow(AccountProperties::Authenticator).to receive(:for_account)
            .with(account_id: account.id)
            .and_return(authenticator)
        end

        context "when subdomain is provided as a param" do
          let(:request_subdomain) { "identity" }

          subject { get :new, params: {account: {subdomain: account.subdomain}} }

          context "when email login is allowed" do
            let(:email_allowed) { true }

            context "when force_saml_sign_in is provided as a param" do
              subject { get :new, params: {account: {subdomain: account.subdomain, force_saml_sign_in: true}} }

              it { is_expected.to redirect_to saml_signin_path(sd: account.subdomain, redirect: "/") }
            end

            it { is_expected.to_not redirect_to saml_signin_path(sd: account.subdomain, redirect: "/") }

            context "and the FusionAuth rollout feature flag is anabled for that account", :launchdarkly do
              before do
                test_data = CA::LaunchDarkly.test_data

                test_data.update(
                  test_data
                    .flag(FeatureFlags::Flags::FUSIONAUTH_ROLLOUT_FLAG)
                    .variation_for_key("account", account.aggregate_id, true)
                )
              end

              it "redirects to /app/auth" do
                subject
                expect(response).to redirect_to("/app/auth")
              end

              context "and a redirect param is included" do
                subject { get :new, params: {account: {subdomain: account.subdomain}, redirect: "/performance/1-1"} }

                it "redirects to /app/auth?redirect=%2Fperformance%2F1-1" do
                  subject
                  expect(response).to redirect_to("/app/auth?redirect=%2Fperformance%2F1-1")
                end
              end
            end
          end
        end

        context "when email login is not allowed" do
          let(:email_allowed) { false }

          it "redirects to saml signin with redirect set to root" do
            is_expected.to redirect_to saml_signin_path(sd: account.subdomain, redirect: "/")
          end

          context "when redirect is set" do
            subject { get :new, params: {redirect: "/somewhere/over/the/rainbow"} }

            it "redirects to saml signin with redirect params" do
              is_expected.to redirect_to saml_signin_path(sd: account.subdomain, redirect: "/somewhere/over/the/rainbow")
            end
          end

          context "when session[:redirect] is set" do
            subject { get :new, params: {redirect: "/somewhere/over/the/rainbow"}, session: {return_to: "/somewhere/over/the/mountain"} }

            it "redirects to saml signin with redirect params" do
              is_expected.to redirect_to saml_signin_path(sd: account.subdomain, redirect: "/somewhere/over/the/mountain")
            end
          end
        end

        context "when email login is allowed" do
          let(:email_allowed) { true }

          it { is_expected.to_not redirect_to saml_signin_path(sd: account.subdomain, redirect: "/") }
        end
      end
    end
  end

  describe "#create" do
    let(:user) { FactoryBot.create(:user, sign_in_count: 1) }
    let(:credentials) { {user: {email: user.email, password: user.password}} }
    let(:authenticator) do
      instance_double(
        AccountProperties::Authenticator,
        email_allowed?: email_allowed,
        saml_allowed?: active_saml
      )
    end
    let(:email_allowed) { true }

    subject { post :create, params: credentials }

    before do
      allow(Person).to receive(:email).with(user.email).and_return(user)
      allow(user).to receive(:account).and_return(account)

      allow(AccountProperties::Authenticator).to receive(:for_account)
        .with(account_id: account.id)
        .and_return(authenticator)
    end

    it "sets an Amplitude Device ID in the session" do
      subject

      expect(session[:amplitude_device_id]).not_to be(nil)
    end

    it "sets an Amplitude Session ID in the session" do
      subject

      expect(session[:amplitude_session_id]).not_to be(nil)
    end

    it "tracks the sign-in event" do
      expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
        user_aggregate_id: user.aggregate_id,
        event_type: "User Signed In",
        event_properties: hash_including(
          "Authentication method" => "Password",
          "Failed attempts" => 0
        ),
        user_properties: {
          "Sign-in count" => 2
        }
      ))

      subject
    end

    context "when they have failed to sign in previously" do
      let(:user) { FactoryBot.create(:user, failed_attempts: 2, sign_in_count: 1) }

      it "tracks the sign-in event including the failed atempts property" do
        expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
          user_aggregate_id: user.aggregate_id,
          event_type: "User Signed In",
          event_properties: hash_including(
            "Failed attempts" => 2
          )
        ))
        subject
      end
    end

    context "when credentials are empty" do
      before do
        allow(Person).to receive(:email).with(nil).and_return(nil)
      end

      context "when params is empty" do
        let(:credentials) { {} }

        it "renders the new template" do
          is_expected.to render_template("new", layout: "plain")
        end
      end

      context "when user params is empty" do
        let(:credentials) { {user: {}} }

        it "renders the new template" do
          is_expected.to render_template("new", layout: "plain")
        end
      end

      context "when user email is empty" do
        let(:credentials) { {user: {password: "foobar"}} }

        it "renders the new template" do
          is_expected.to render_template("new", layout: "plain")
        end
      end
    end

    context "when email login is not allowed" do
      let(:email_allowed) { false }

      it { is_expected.to redirect_to new_user_session_path }
    end

    context "when the account has no active saml integration" do
      let(:active_saml) { false }

      it { is_expected.to redirect_to root_path }
    end

    context "when the account has saml integration" do
      let(:active_saml) { true }

      context "when email login is not allowed" do
        let(:email_allowed) { false }

        it { is_expected.to redirect_to saml_signin_path(sd: account.subdomain, redirect: "/") }
      end

      context "when email login is allowed" do
        let(:email_allowed) { true }

        it "redirects to root path" do
          is_expected.to redirect_to root_path
        end

        context "when a user signs in with invalid credentials" do
          let(:credentials) { {user: {email: user.email, password: "wrong_password_wrong"}} }

          it "renders the login page" do
            is_expected.to render_template("new", layout: "plain")
          end
        end

        context "when the user does not have a password set" do
          let(:user) { FactoryBot.create(:user, :no_password) }

          it "redirects the user to their SAML IdP" do
            is_expected.to redirect_to saml_signin_path(sd: account.subdomain, redirect: "/")
          end
        end
      end
    end
  end

  describe "#new_sign_in" do
    subject { get :new_sign_in }

    context "when there is no FusionAuth Authorization header" do
      before do
        request.delete_header("X-CA-FA-Authorization")
        request.delete_header("NewAuthorization")
      end

      it "raise AuthorizationHeaderError in Sentry" do
        expect(Sentry).to receive(:capture_exception).with(JwtAuthentication::JWTDecoderError)

        subject
      end

      it "render 401 error page" do
        subject

        expect(response.code).to eq("401")
        expect(response).to render_template("site/401.html.haml")
      end
    end

    context "when there is no bearer token" do
      before do
        request.headers["X-CA-FA-Authorization"] = "invalid"
        request.headers["NewAuthorization"] = "invalid"
      end

      it "raise AuthorizationHeaderError in Sentry" do
        expect(Sentry).to receive(:capture_exception).with(JwtAuthentication::JWTDecoderError)

        subject
      end

      it "render 401 error page" do
        subject

        expect(response.code).to eq("401")
        expect(response).to render_template("site/401.html.haml")
      end
    end

    context "when failed to verify jwt signature" do
      before do
        request.headers["X-CA-FA-Authorization"] = "Bearer invalid_token"
        request.headers["NewAuthorization"] = "Bearer invalid_token"
        expect(JWT).to receive(:decode).and_raise(JWT::DecodeError).exactly(4).times
      end

      it "raise AuthorizationHeaderError in Sentry" do
        expect(Sentry).to receive(:capture_exception).with(JwtAuthentication::JWTDecoderError)

        subject
      end

      it "render 401 error page" do
        subject

        expect(response.code).to eq("401")
        expect(response).to render_template("site/401.html.haml")
      end
    end

    context "when verify jwt signature successfully" do
      let(:account) { FactoryBot.create(:account) }
      let(:user) { FactoryBot.create(:person, account: account, password: "dummy-password") }

      before do
        request.headers["X-CA-FA-Authorization"] = "Bearer valid_token"
        request.headers["NewAuthorization"] = "Bearer valid_token"
      end

      context "when effective user is not found" do
        before do
          expect(JWT).to receive(:decode).and_return([{"effectiveUserId" => "0000-0000-0000-0000", "accountId" => account.aggregate_id, "realUserId" => user.aggregate_id}])
        end

        it "raise UserNotFoundError in Sentry" do
          expect(Sentry).to receive(:capture_exception).with(ApplicationController::UserNotFoundError)

          subject
        end

        it "render 401 error page" do
          subject

          expect(response.code).to eq("401")
          expect(response).to render_template("site/401.html.haml")
        end
      end

      context "when real user is not found" do
        before do
          expect(JWT).to receive(:decode).and_return([{"effectiveUserId" => user.aggregate_id, "accountId" => account.aggregate_id, "realUserId" => "0000-0000-0000-0000"}])
        end

        it "raise UserNotFoundError in Sentry" do
          expect(Sentry).to receive(:capture_exception).with(ApplicationController::RealUserNotFoundError)

          subject
        end

        it "render 401 error page" do
          subject

          expect(response.code).to eq("401")
          expect(response).to render_template("site/401.html.haml")
        end
      end

      context "when user is found" do
        context "when feature flag is off" do
          before do
            expect(JWT).to receive(:decode).and_return([{"effectiveUserId" => user.aggregate_id, "accountId" => account.aggregate_id, "realUserId" => user.aggregate_id}])
          end

          it "render 403 error page" do
            subject

            expect(response.code).to eq("403")
            expect(response).to render_template("site/403.html.haml")
          end
        end

        context "when feature flag is on" do
          before do
            query = double "feature_flags"
            allow(FeatureFlags::Queries::ValueForAccount).to receive(:new).and_return(query)
            allow(query).to receive(:call).with(
              account_aggregate_id: account.aggregate_id,
              flag_name: FeatureFlags::Flags::SIGN_IN_WITH_NEW_AUTH_JWT,
              fallback_value: false
            ).and_return(true)
          end

          context "when failed to validate identity" do
            before do
              expect(JWT).to receive(:decode).and_return([{"effectiveUserId" => user.aggregate_id, "accountId" => "123", "realUserId" => user.aggregate_id}])
            end

            it "raise IdentityNotMatchError in Sentry" do
              expect(Sentry).to receive(:capture_exception).with(described_class::IdentityNotMatchError)

              subject
            end

            it "render 500 error page" do
              subject

              expect(response.code).to eq("500")
              expect(response).to render_template("site/500.html.haml")
            end
          end

          context "when validate identity successfully" do
            before do
              expect(JWT).to receive(:decode).and_return([{"email" => user.email, "accountId" => user.account.aggregate_id, "effectiveUserId" => user.aggregate_id, "realUserId" => user.aggregate_id}])
            end

            it "sign in user" do
              subject

              expect(controller.warden.user).to eq(controller.current_user)
              expect(controller.current_user).to eq(user)
            end

            it "sets created_at in the session after successful sign-in" do
              subject

              expect(session[:created_at]).to be_present
              expect(session[:created_at]).to be_within(1.second).of(Time.current.utc)
            end

            it "redirect to home page if no session[:return_to] or params[:redirect]" do
              subject

              expect(response.status).to eq(302)
              expect(response).to redirect_to root_path
            end

            it "redirect to correct path if only has session[:return_to]" do
              redirect_path = "/one-to-one"
              request.session[:return_to] = redirect_path

              subject

              expect(response.status).to eq(302)
              expect(response).to redirect_to redirect_path
            end

            it "redirect to correct path if only has params[:redirect]" do
              redirect_path = "/performance-review"
              get :new_sign_in, params: {redirect: redirect_path}

              expect(response.status).to eq(302)
              expect(response).to redirect_to redirect_path
            end

            it "redirect to params[:redirect] if both have session[:return_to] and params[:redirect]" do
              session_path = "/one-to-one"
              params_path = "/performance-review"

              request.session[:return_to] = session_path

              get :new_sign_in, params: {redirect: params_path}

              expect(response.status).to eq(302)
              expect(response).to redirect_to params_path
            end
          end
        end
      end
    end
  end

  describe "#destroy" do
    let(:user) { FactoryBot.create(:user) }
    let(:jwt_sid) { "fake-jwt-sid" }

    before do
      sign_in(user)
    end

    context "when fusionauth_jwt_post_signin_enabled? returns true" do
      let(:refresh_token) { "fake-refresh-token" }
      let(:fa_jwt_cookie_name) { "cultureamp.development-us.token" }
      let(:refresh_token_cookie_name) { "cultureamp.development-us.refresh-token" }

      before do
        allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).with(user).and_return(true)
        allow(controller).to receive(:jwt_sid).and_return(jwt_sid)
        allow(controller).to receive(:delete_fusionauth_entity).with(jwt_sid)
        allow(controller).to receive(:sign_out_fusionauth)
      end

      it "deletes fusionauth entity" do
        expect(controller).to receive(:delete_fusionauth_entity).with(jwt_sid)
        expect(controller).to receive(:sign_out_fusionauth).with(user_id: user.aggregate_id, refresh_token: refresh_token)

        request.cookies[refresh_token_cookie_name] = refresh_token
        delete :destroy, as: :json

        expect(response.status).to eq(204)
        expect(response.headers["Set-Cookie"]).to include("cultureamp.development-us.token=; Domain=culture-amp.com; Path=/; Max-Age=0; SameSite=lax")
        expect(response.headers["Set-Cookie"]).to include("cultureamp.development-us.refresh-token=; Domain=culture-amp.com; Path=/; Max-Age=0; SameSite=lax")
      end
    end

    context "when fusionauth_jwt_post_signin_enabled? returns false" do
      before do
        allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).with(user).and_return(false)
      end

      it "signs the user out" do
        expect(controller.current_user).to eq(user)
        expect(controller).to receive(:sign_out).and_call_original
        delete :destroy
      end

      it "redirects to the sign out path" do
        delete :destroy, as: :json
        expect(response.status).to eq(204)
      end
    end
  end

  describe "#require_no_authentication" do
    let(:user) { FactoryBot.create(:user, account: account) }
    let(:subdomain) { "test-subdomain" }
    let(:feature_flag_query) { instance_double(FeatureFlags::Queries::ValueForContext) }

    subject { controller.send(:require_no_authentication) }

    before do
      allow(controller).to receive(:get_subdomain_from_request).and_return(subdomain)
      allow(FeatureFlags::Queries::ValueForContext).to receive(:new).and_return(feature_flag_query)
      @request.host = "#{subdomain}.#{Murmur::CULTURE_AMP_DOMAIN}"
    end

    context "when fusionauth_jwt_post_signin feature flag is disabled" do
      before do
        allow(feature_flag_query).to receive(:call).with(
          flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN,
          subdomain: subdomain,
          fallback_value: false
        ).and_return(false)
        allow_any_instance_of(Devise::SessionsController).to receive(:require_no_authentication)
      end

      it "calls super (default Devise behavior)" do
        subject

        expect(feature_flag_query).to have_received(:call).with(
          flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN,
          subdomain: subdomain,
          fallback_value: false
        )
      end
    end

    context "when fusionauth_jwt_post_signin feature flag is enabled" do
      before do
        allow(feature_flag_query).to receive(:call).with(
          flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN,
          subdomain: subdomain,
          fallback_value: false
        ).and_return(true)
      end

      context "when current_user is present" do
        before do
          allow(controller).to receive(:current_user).and_return(user)
          allow(controller).to receive(:after_sign_in_path_for).with(user).and_return("/dashboard")
          allow(controller).to receive(:redirect_to)
        end

        it "redirects to after_sign_in_path_for the current user" do
          subject

          expect(controller).to have_received(:redirect_to).with("/dashboard")
        end

        it "calls after_sign_in_path_for with the current user" do
          subject

          expect(controller).to have_received(:after_sign_in_path_for).with(user)
        end

        it "does not continue to the next method call" do
          # Mock super to ensure it's not called
          allow_any_instance_of(Devise::SessionsController).to receive(:require_no_authentication)

          subject

          expect_any_instance_of(Devise::SessionsController).not_to have_received(:require_no_authentication)
        end
      end

      context "when current_user is not present" do
        before do
          allow(controller).to receive(:current_user).and_return(nil)
          allow(controller).to receive(:redirect_to)
        end

        it "does not redirect" do
          subject

          expect(controller).not_to have_received(:redirect_to)
        end

        it "continues to the next method call (does nothing)" do
          # The method should complete without error and without calling super
          expect { subject }.not_to raise_error
        end
      end

      context "when an error occurs during JWT processing" do
        let(:error_message) { "JWT processing error" }

        before do
          allow(controller).to receive(:current_user).and_raise(StandardError.new(error_message))
          allow(Rails.logger).to receive(:debug)
          allow(controller).to receive(:redirect_to)
        end

        it "logs the error and continues to the next method call" do
          subject

          expect(Rails.logger).to have_received(:debug).with("JWT processing failed in require_no_authentication: #{error_message}")
        end

        it "does not redirect" do
          subject

          expect(controller).not_to have_received(:redirect_to)
        end

        it "does not re-raise the error" do
          expect { subject }.not_to raise_error
        end
      end
    end
  end
end
